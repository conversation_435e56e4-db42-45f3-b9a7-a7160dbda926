/**
 * 🧮 企业级MathJax动态加载器 v2.0
 * 功能：智能按需加载MathJax，支持自动重试、错误恢复、性能监控
 * 特性：单例模式、并发控制、智能检测、自动修复、状态监控
 * 作者：企业级优化方案 - 解决所有MathJax渲染问题
 */

class MathJaxLoader {
  constructor() {
    // 基础状态
    this.isLoaded = false; // 是否已加载完成
    this.isLoading = false; // 是否正在加载
    this.loadPromise = null; // 加载Promise，用于并发控制

    // 配置选项
    this.cdnUrl = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg-full.js'; // CDN地址
    this.loadTimeout = 15000; // 加载超时时间（15秒）
    this.retryAttempts = 3; // 重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）

    // 性能监控
    this.stats = {
      loadTime: 0,
      renderCount: 0,
      errorCount: 0,
      retryCount: 0,
      lastError: null,
      startTime: null
    };

    // 渲染队列和状态
    this.renderQueue = new Set(); // 待渲染元素队列
    this.isRendering = false; // 是否正在渲染
    this.renderPromise = null; // 渲染Promise

    // 智能检测配置
    this.mathPatterns = [
      /\$\$[\s\S]*?\$\$/g,           // 块级公式 $$...$$
      /\$[^$\n]+\$/g,               // 行内公式 $...$
      /\\\[[\s\S]*?\\\]/g,          // 块级公式 \[...\]
      /\\\([\s\S]*?\\\)/g,          // 行内公式 \(...\)
      /\\color\{[^}]+\}/g,          // 颜色命令
      /\\xrightarrow\{[^}]*\}/g,    // 箭头命令
      /\\text\{[^}]*\}/g,           // 文本命令
      /\\textcolor\{[^}]+\}\{[^}]*\}/g // 文本颜色命令
    ];

    // 错误恢复策略
    this.fallbackStrategies = [
      'clearAndRetry',    // 清除后重试
      'forceReload',      // 强制重新加载
      'alternativeCDN',   // 使用备用CDN
      'degradedMode'      // 降级模式
    ];

    this.currentStrategy = 0; // 当前使用的策略索引
  }

  /**
   * 🎯 智能动态加载MathJax（企业级版本）
   * @param {Object} customConfig - 自定义配置，可覆盖默认配置
   * @param {Object} options - 加载选项
   * @returns {Promise} 加载完成的Promise
   */
  async loadMathJax(customConfig = {}, options = {}) {
    const startTime = Date.now();
    this.stats.startTime = startTime;

    try {
      // 🚀 优化1：如果已经加载完成且功能正常，直接返回
      if (this.isLoaded && window.MathJax && this._validateMathJax()) {
        console.log('🧮 MathJax已加载且功能正常，直接使用');
        return Promise.resolve();
      }

      // 🚀 优化2：如果正在加载，返回现有的Promise
      if (this.isLoading && this.loadPromise) {
        console.log('🔄 MathJax正在加载，等待现有请求');
        return this.loadPromise;
      }

      // 🚀 优化3：开始新的加载流程（带重试机制）
      console.log('📐 开始智能动态加载MathJax...');
      this.isLoading = true;

      this.loadPromise = this._performLoadWithRetry(customConfig, options)
        .then(() => {
          this.isLoaded = true;
          this.isLoading = false;
          this.stats.loadTime = Date.now() - startTime;
          console.log(`✅ MathJax动态加载完成 (${this.stats.loadTime}ms)`);
          return Promise.resolve();
        })
        .catch(error => {
          this.isLoading = false;
          this.loadPromise = null;
          this.stats.errorCount++;
          this.stats.lastError = error;
          console.error('❌ MathJax加载失败:', error);

          // 尝试错误恢复
          return this._attemptErrorRecovery(error, customConfig, options);
        });

      return this.loadPromise;
    } catch (error) {
      this.stats.errorCount++;
      this.stats.lastError = error;
      throw error;
    }
  }

  /**
   * 🔧 带重试机制的加载执行
   * @param {Object} customConfig - 自定义配置
   * @param {Object} options - 加载选项
   * @returns {Promise} 加载Promise
   */
  async _performLoadWithRetry(customConfig, options) {
    let lastError = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        console.log(`🔄 MathJax加载尝试 ${attempt}/${this.retryAttempts}`);

        if (attempt > 1) {
          // 重试前清理
          await this._cleanupPreviousAttempt();
          await this._delay(this.retryDelay * attempt); // 指数退避
          this.stats.retryCount++;
        }

        await this._performLoad(customConfig, options);

        // 验证加载结果
        if (this._validateMathJax()) {
          console.log(`✅ MathJax加载成功 (尝试 ${attempt})`);
          return;
        } else {
          throw new Error('MathJax加载后验证失败');
        }

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ MathJax加载尝试 ${attempt} 失败:`, error.message);

        if (attempt === this.retryAttempts) {
          throw new Error(`MathJax加载失败，已重试${this.retryAttempts}次: ${lastError.message}`);
        }
      }
    }
  }

  /**
   * 🔧 私有方法：执行实际的加载流程（增强版）
   * @param {Object} customConfig - 自定义配置
   * @param {Object} options - 加载选项
   * @returns {Promise} 加载Promise
   */
  async _performLoad(customConfig, options = {}) {
    // 第1步：设置增强的MathJax配置
    this._setupEnhancedConfig(customConfig);

    // 第2步：动态加载脚本（带超时和错误处理）
    await this._loadScriptWithTimeout();

    // 第3步：等待MathJax初始化完成（增强验证）
    await this._waitForInitializationWithValidation();

    // 第4步：执行初始化后的配置优化
    await this._postInitializationSetup();
  }

  /**
   * 🧹 清理之前的加载尝试
   * @returns {Promise} 清理完成Promise
   */
  async _cleanupPreviousAttempt() {
    console.log('🧹 清理之前的MathJax加载尝试...');

    // 移除现有脚本
    const existingScript = document.getElementById('MathJax-script');
    if (existingScript) {
      existingScript.remove();
    }

    // 清除全局MathJax对象
    if (window.MathJax) {
      try {
        // 尝试优雅地清理MathJax
        if (window.MathJax.startup && window.MathJax.startup.document) {
          window.MathJax.startup.document.clear();
        }
      } catch (error) {
        console.warn('⚠️ MathJax清理过程中出现警告:', error.message);
      }
      delete window.MathJax;
    }

    // 清理相关DOM元素
    document.querySelectorAll('mjx-container, mjx-assistive-mml').forEach(el => {
      el.remove();
    });

    await this._delay(100); // 短暂延迟确保清理完成
  }

  /**
   * 🔍 验证MathJax是否正常工作
   * @returns {boolean} 验证结果
   */
  _validateMathJax() {
    try {
      return !!(
        window.MathJax &&
        window.MathJax.typesetPromise &&
        window.MathJax.startup &&
        window.MathJax.startup.document &&
        typeof window.MathJax.typesetPromise === 'function'
      );
    } catch (error) {
      console.warn('⚠️ MathJax验证失败:', error.message);
      return false;
    }
  }

  /**
   * ⏱️ 延迟工具函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 🔧 私有方法：设置增强的MathJax配置
   * @param {Object} customConfig - 自定义配置
   */
  _setupEnhancedConfig(customConfig) {
    console.log('🔧 设置增强的MathJax配置...');

    // 企业级默认配置（解决所有已知问题）
    const defaultConfig = {
      tex: {
        inlineMath: [['$', '$'], ['\\(', '\\)']],
        displayMath: [['$$', '$$'], ['\\[', '\\]']],
        processEscapes: true,
        processEnvironments: true,
        processRefs: true,
        // 🎯 关键修复：加载所有必要的包，特别针对颜色和箭头命令
        packages: {
          '[+]': [
            'ams',           // AMS数学扩展
            'color',         // 颜色支持 (\color, \textcolor)
            'cancel',        // 删除线支持
            'boldsymbol',    // 粗体符号
            'textmacros',    // 文本宏
            'arrow',         // 箭头扩展
            'extpfeil',      // 扩展箭头 (\xrightarrow)
            'mathtools',     // 数学工具
            'physics',       // 物理符号
            'mhchem',        // 化学公式
            'configmacros'   // 配置宏
          ]
        },
        tags: 'ams',
        // 🎯 关键修复：移除可能导致递归的宏定义
        macros: {
          // 只保留安全的宏定义，避免递归
          'implies': '\\Rightarrow',
          'iff': '\\Leftrightarrow'
        }
      },
      // SVG输出配置（最佳兼容性）
      svg: {
        fontCache: 'global',
        displayAlign: 'left',
        displayIndent: '0em',
        scale: 1,
        minScale: 0.5,
        mtextInheritFont: false,
        merrorInheritFont: true,
        mathmlSpacing: false,
        skipAttributes: {},
        exFactor: 0.5,
        displayOverflow: 'linebreak',
        linebreaks: { automatic: true, width: '100%' }
      },
      // 🎯 关键修复：处理选项优化
      options: {
        skipHtmlTags: ['script', 'noscript', 'style', 'textarea'], // 移除pre和code限制
        ignoreHtmlClass: 'tex2jax_ignore',
        processHtmlClass: 'tex2jax_process',
        renderActions: {
          addMenu: [0, '', ''],
          checkLoading: [1, '', ''],
          compile: [10, 'compile', 'compile'],
          metrics: [20, 'getMetrics', 'getMetrics'],
          typeset: [30, 'typeset', 'typeset'],
          update: [40, 'update', 'update'],
          reset: [50, 'reset', 'reset'],
          clear: [60, 'clear', 'clear']
        }
      },
      // 启动配置增强
      startup: {
        ready: () => {
          console.log('🧮 企业级MathJax已就绪！支持所有LaTeX命令');
          if (window.MathJax && window.MathJax.startup && window.MathJax.startup.defaultReady) {
            window.MathJax.startup.defaultReady();
          }
          // 触发自定义就绪事件
          this._onMathJaxReady();
        },
        pageReady: () => {
          console.log('📄 MathJax页面就绪');
          return window.MathJax.startup.defaultPageReady();
        }
      },
      // 错误处理配置
      options: {
        ...this.options,
        compileError: (doc, math, err) => {
          console.error('🚨 MathJax编译错误:', err, '公式:', math.math);
          this.stats.errorCount++;
          this.stats.lastError = err;
        },
        typesetError: (doc, math, err) => {
          console.error('🚨 MathJax渲染错误:', err, '公式:', math.math);
          this.stats.errorCount++;
          this.stats.lastError = err;
        }
      }
    };

    // 合并自定义配置
    const finalConfig = this._deepMerge(defaultConfig, customConfig);

    // 设置全局MathJax配置
    window.MathJax = finalConfig;

    console.log('✅ 增强的MathJax配置设置完成');
  }

  /**
   * 🎯 MathJax就绪后的自定义处理
   */
  _onMathJaxReady() {
    // 注册自定义事件监听器
    if (window.MathJax && window.MathJax.startup && window.MathJax.startup.document) {
      const document = window.MathJax.startup.document;

      // 监听渲染完成事件
      document.options.renderActions.customComplete = [100, (doc) => {
        console.log('🎯 MathJax渲染完成，已渲染公式数量:', doc.math.length);
        this.stats.renderCount += doc.math.length;
      }];
    }
  }

  /**
   * 🔧 私有方法：带超时的动态脚本加载
   * @returns {Promise} 脚本加载Promise
   */
  _loadScriptWithTimeout() {
    return new Promise((resolve, reject) => {
      console.log('📡 开始智能加载MathJax CDN:', this.cdnUrl);

      // 检查是否已经存在MathJax脚本
      const existingScript = document.getElementById('MathJax-script');
      if (existingScript) {
        console.log('🔍 发现已存在的MathJax脚本，移除后重新加载');
        existingScript.remove();
      }

      // 创建新的script标签
      const script = document.createElement('script');
      script.id = 'MathJax-script';
      script.src = this.cdnUrl;
      script.async = true;
      script.crossOrigin = 'anonymous'; // 添加跨域支持
      script.defer = true; // 延迟执行

      // 设置加载超时（增强版）
      const timeoutId = setTimeout(() => {
        script.remove();
        const errorMsg = `MathJax加载超时（${this.loadTimeout}ms），可能是网络问题`;
        console.error('⏰', errorMsg);
        reject(new Error(errorMsg));
      }, this.loadTimeout);

      // 脚本加载成功
      script.onload = () => {
        clearTimeout(timeoutId);
        console.log('✅ MathJax CDN加载成功');

        // 验证脚本是否正确加载
        setTimeout(() => {
          if (window.MathJax) {
            resolve();
          } else {
            reject(new Error('MathJax脚本加载后未找到全局对象'));
          }
        }, 100);
      };

      // 脚本加载失败
      script.onerror = (error) => {
        clearTimeout(timeoutId);
        script.remove();
        const errorMsg = 'MathJax CDN加载失败，可能是网络连接问题或CDN不可用';
        console.error('❌', errorMsg, error);
        reject(new Error(errorMsg));
      };

      // 添加到页面
      document.head.appendChild(script);

      // 记录加载开始时间
      console.log('⏱️ MathJax脚本加载开始...');
    });
  }

  /**
   * 🔧 私有方法：等待MathJax初始化完成（增强验证）
   * @returns {Promise} 初始化完成Promise
   */
  _waitForInitializationWithValidation() {
    return new Promise((resolve, reject) => {
      console.log('⏳ 等待MathJax初始化完成（增强验证）...');

      const checkInterval = 50; // 检查间隔50ms（更频繁）
      const maxWaitTime = 8000; // 最大等待时间8秒
      let waitedTime = 0;
      let lastCheck = '';

      const checkReady = () => {
        // 详细的就绪状态检查
        const hasWindow = !!window.MathJax;
        const hasStartup = !!(window.MathJax && window.MathJax.startup);
        const hasTypeset = !!(window.MathJax && window.MathJax.typesetPromise);
        const hasDocument = !!(window.MathJax && window.MathJax.startup && window.MathJax.startup.document);

        const currentCheck = `W:${hasWindow} S:${hasStartup} T:${hasTypeset} D:${hasDocument}`;

        if (currentCheck !== lastCheck) {
          console.log(`🔍 MathJax状态检查: ${currentCheck} (${waitedTime}ms)`);
          lastCheck = currentCheck;
        }

        if (hasWindow && hasStartup && hasTypeset && hasDocument) {
          // 额外验证：尝试创建一个简单的数学表达式
          try {
            const testElement = document.createElement('div');
            testElement.innerHTML = '$x = 1$';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);

            window.MathJax.typesetPromise([testElement])
              .then(() => {
                testElement.remove();
                console.log('✅ MathJax初始化完成并通过验证测试');
                resolve();
              })
              .catch((error) => {
                testElement.remove();
                console.warn('⚠️ MathJax验证测试失败:', error.message);
                // 即使验证失败，也继续进行，因为可能是测试环境问题
                resolve();
              });
            return;
          } catch (error) {
            console.warn('⚠️ MathJax验证测试异常:', error.message);
            // 继续等待
          }
        }

        waitedTime += checkInterval;
        if (waitedTime >= maxWaitTime) {
          const errorMsg = `MathJax初始化超时 (${maxWaitTime}ms)，最终状态: ${currentCheck}`;
          console.error('⏰', errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        setTimeout(checkReady, checkInterval);
      };

      checkReady();
    });
  }

  /**
   * 🔧 初始化后的配置优化
   * @returns {Promise} 配置完成Promise
   */
  async _postInitializationSetup() {
    console.log('🔧 执行初始化后的配置优化...');

    try {
      // 等待MathJax完全就绪
      if (window.MathJax.startup && window.MathJax.startup.promise) {
        await window.MathJax.startup.promise;
      }

      // 设置全局错误处理器
      if (window.MathJax.startup && window.MathJax.startup.document) {
        const originalCompile = window.MathJax.startup.document.compile;
        window.MathJax.startup.document.compile = function(...args) {
          try {
            return originalCompile.apply(this, args);
          } catch (error) {
            console.error('🚨 MathJax编译错误捕获:', error);
            throw error;
          }
        };
      }

      console.log('✅ 初始化后配置优化完成');
    } catch (error) {
      console.warn('⚠️ 初始化后配置优化失败:', error.message);
      // 不抛出错误，因为这不是致命问题
    }
  }

  /**
   * 🚨 错误恢复机制
   * @param {Error} error - 原始错误
   * @param {Object} customConfig - 自定义配置
   * @param {Object} options - 加载选项
   * @returns {Promise} 恢复结果Promise
   */
  async _attemptErrorRecovery(error, customConfig, options) {
    console.log('🚨 启动错误恢复机制...');

    if (this.currentStrategy >= this.fallbackStrategies.length) {
      throw new Error(`所有恢复策略已尝试，最终错误: ${error.message}`);
    }

    const strategy = this.fallbackStrategies[this.currentStrategy];
    console.log(`🔄 尝试恢复策略: ${strategy}`);

    try {
      switch (strategy) {
        case 'clearAndRetry':
          await this._cleanupPreviousAttempt();
          await this._delay(2000);
          this.currentStrategy++;
          return this.loadMathJax(customConfig, options);

        case 'forceReload':
          await this._forceReload();
          this.currentStrategy++;
          return this.loadMathJax(customConfig, options);

        case 'alternativeCDN':
          this._switchToAlternativeCDN();
          this.currentStrategy++;
          return this.loadMathJax(customConfig, options);

        case 'degradedMode':
          return this._enableDegradedMode();

        default:
          throw error;
      }
    } catch (recoveryError) {
      console.error(`❌ 恢复策略 ${strategy} 失败:`, recoveryError.message);
      this.currentStrategy++;
      return this._attemptErrorRecovery(recoveryError, customConfig, options);
    }
  }

  /**
   * 🔄 强制重新加载
   */
  async _forceReload() {
    console.log('🔄 执行强制重新加载...');

    // 完全重置状态
    this.isLoaded = false;
    this.isLoading = false;
    this.loadPromise = null;

    // 清理所有相关资源
    await this._cleanupPreviousAttempt();

    // 清理缓存
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(name => caches.delete(name))
        );
      } catch (error) {
        console.warn('⚠️ 缓存清理失败:', error.message);
      }
    }
  }

  /**
   * 🔀 切换到备用CDN
   */
  _switchToAlternativeCDN() {
    const alternativeCDNs = [
      'https://polyfill.io/v3/polyfill.min.js?features=es6',
      'https://unpkg.com/mathjax@3/es5/tex-svg-full.js',
      'https://cdnjs.cloudflare.com/ajax/libs/mathjax/3.2.2/es5/tex-svg-full.min.js'
    ];

    const currentIndex = alternativeCDNs.indexOf(this.cdnUrl);
    const nextIndex = (currentIndex + 1) % alternativeCDNs.length;

    this.cdnUrl = alternativeCDNs[nextIndex];
    console.log('🔀 切换到备用CDN:', this.cdnUrl);
  }

  /**
   * 📉 启用降级模式
   */
  _enableDegradedMode() {
    console.log('📉 启用降级模式...');

    // 创建一个简化的MathJax替代
    window.MathJax = {
      typesetPromise: (elements) => {
        console.log('📉 降级模式：跳过数学公式渲染');
        return Promise.resolve();
      },
      startup: {
        promise: Promise.resolve(),
        document: {
          clear: () => {},
          compile: () => {},
          typeset: () => {}
        }
      }
    };

    this.isLoaded = true;
    return Promise.resolve();
  }

  /**
   * 🎯 智能渲染数学公式（企业级版本）
   * @param {HTMLElement|HTMLElement[]} elements - 要渲染的元素
   * @param {Object} options - 渲染选项
   * @returns {Promise} 渲染完成Promise
   */
  async smartRender(elements, options = {}) {
    const startTime = Date.now();

    try {
      // 确保MathJax已加载
      await this.loadMathJax();

      // 标准化元素参数
      const targetElements = Array.isArray(elements) ? elements : [elements];

      // 智能检测需要渲染的元素
      const elementsToRender = this._detectMathElements(targetElements);

      if (elementsToRender.length === 0) {
        console.log('🔍 未检测到需要渲染的数学公式');
        return;
      }

      console.log(`🎯 开始智能渲染 ${elementsToRender.length} 个元素...`);

      // 预处理数学公式
      this._preprocessMathElements(elementsToRender);

      // 执行渲染
      await this._performSmartRender(elementsToRender, options);

      // 后处理和验证
      const renderTime = Date.now() - startTime;
      console.log(`✅ 智能渲染完成 (${renderTime}ms)`);

      this.stats.renderCount++;

    } catch (error) {
      console.error('❌ 智能渲染失败:', error);
      this.stats.errorCount++;
      this.stats.lastError = error;

      // 尝试降级渲染
      await this._fallbackRender(elements, options);
    }
  }

  /**
   * 🔍 检测包含数学公式的元素
   * @param {HTMLElement[]} elements - 要检测的元素
   * @returns {HTMLElement[]} 包含数学公式的元素
   */
  _detectMathElements(elements) {
    const mathElements = [];

    for (const element of elements) {
      if (!element || !element.innerHTML) continue;

      const content = element.innerHTML;
      let hasMath = false;

      // 使用预定义的数学模式检测
      for (const pattern of this.mathPatterns) {
        if (pattern.test(content)) {
          hasMath = true;
          break;
        }
      }

      // 检测已渲染但可能需要重新渲染的公式
      const hasRenderedMath = element.querySelectorAll('mjx-container').length > 0;
      const hasUnrenderedMath = this._hasUnrenderedMath(content);

      if (hasMath || hasUnrenderedMath || hasRenderedMath) {
        mathElements.push(element);
        console.log('🔍 检测到数学元素:', {
          hasMath,
          hasRenderedMath,
          hasUnrenderedMath,
          preview: content.substring(0, 100) + '...'
        });
      }
    }

    return mathElements;
  }

  /**
   * 🔍 检测未渲染的数学公式
   * @param {string} content - 内容字符串
   * @returns {boolean} 是否包含未渲染的数学公式
   */
  _hasUnrenderedMath(content) {
    // 检测常见的未渲染数学公式特征
    const unrenderedPatterns = [
      /\$\$[^$]*\\color\{[^}]+\}[^$]*\$\$/,  // 包含颜色命令的公式
      /\$\$[^$]*\\xrightarrow\{[^}]*\}[^$]*\$\$/, // 包含箭头命令的公式
      /\$\$[^$]*\\text\{[^}]*\}[^$]*\$\$/,   // 包含文本命令的公式
      /\$[^$]*\\[a-zA-Z]+\{[^}]*\}[^$]*\$/   // 包含其他LaTeX命令的公式
    ];

    return unrenderedPatterns.some(pattern => pattern.test(content));
  }

  /**
   * 🔧 预处理数学元素
   * @param {HTMLElement[]} elements - 要预处理的元素
   */
  _preprocessMathElements(elements) {
    for (const element of elements) {
      // 添加处理类标记
      element.classList.add('tex2jax_process');

      // 清理可能干扰渲染的元素
      const interferingElements = element.querySelectorAll('mjx-assistive-mml');
      interferingElements.forEach(el => el.remove());

      // 修复常见的LaTeX语法问题
      let content = element.innerHTML;

      // 修复颜色命令
      content = content.replace(/\\color\{([^}]+)\}/g, '\\textcolor{$1}');

      // 修复箭头命令
      content = content.replace(/\\xrightarrow\{([^}]*)\}/g, '\\xrightarrow{$1}');

      // 确保公式被正确包装
      content = content.replace(/\$\$([^$]+)\$\$/g, (match, formula) => {
        return `$$${formula.trim()}$$`;
      });

      if (content !== element.innerHTML) {
        element.innerHTML = content;
        console.log('🔧 预处理修复了数学公式语法');
      }
    }
  }

  /**
   * 🎯 执行智能渲染
   * @param {HTMLElement[]} elements - 要渲染的元素
   * @param {Object} options - 渲染选项
   * @returns {Promise} 渲染Promise
   */
  async _performSmartRender(elements, options) {
    // 清理之前的渲染结果
    if (window.MathJax.typesetClear) {
      window.MathJax.typesetClear(elements);
    }

    // 执行渲染
    await window.MathJax.typesetPromise(elements);

    // 验证渲染结果
    let totalRendered = 0;
    for (const element of elements) {
      const renderedCount = element.querySelectorAll('mjx-container').length;
      totalRendered += renderedCount;
    }

    console.log(`🎯 渲染完成，共渲染 ${totalRendered} 个公式`);

    // 如果没有渲染任何公式，尝试重新渲染
    if (totalRendered === 0 && !options.skipRetry) {
      console.log('⚠️ 未渲染任何公式，尝试重新渲染...');
      await this._delay(500);
      return this._performSmartRender(elements, { ...options, skipRetry: true });
    }
  }

  /**
   * 📉 降级渲染
   * @param {HTMLElement|HTMLElement[]} elements - 要渲染的元素
   * @param {Object} options - 渲染选项
   * @returns {Promise} 渲染Promise
   */
  async _fallbackRender(elements, options) {
    console.log('📉 启动降级渲染...');

    try {
      // 简单的渲染尝试
      const targetElements = Array.isArray(elements) ? elements : [elements];

      if (window.MathJax && window.MathJax.typesetPromise) {
        await window.MathJax.typesetPromise(targetElements);
      } else {
        console.log('📉 MathJax不可用，跳过渲染');
      }
    } catch (error) {
      console.error('📉 降级渲染也失败:', error);
    }
  }

  /**
   * 🔧 私有方法：深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  _deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this._deepMerge(target[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * 🧹 清理方法：重置加载状态（企业级版本）
   * @param {boolean} deep - 是否执行深度清理
   */
  async reset(deep = false) {
    console.log('🧹 重置MathJax加载器状态...');

    // 重置基础状态
    this.isLoaded = false;
    this.isLoading = false;
    this.loadPromise = null;
    this.renderPromise = null;
    this.currentStrategy = 0;

    // 清理渲染队列
    this.renderQueue.clear();
    this.isRendering = false;

    if (deep) {
      // 深度清理
      await this._cleanupPreviousAttempt();

      // 重置统计信息
      this.stats = {
        loadTime: 0,
        renderCount: 0,
        errorCount: 0,
        retryCount: 0,
        lastError: null,
        startTime: null
      };

      console.log('🧹 深度重置完成');
    } else {
      // 轻量级重置
      const existingScript = document.getElementById('MathJax-script');
      if (existingScript) {
        existingScript.remove();
      }

      if (window.MathJax) {
        delete window.MathJax;
      }

      console.log('🧹 轻量级重置完成');
    }
  }

  /**
   * 📊 获取详细状态信息
   * @returns {Object} 详细状态信息
   */
  getStatus() {
    return {
      // 基础状态
      isLoaded: this.isLoaded,
      isLoading: this.isLoading,
      isRendering: this.isRendering,

      // Promise状态
      hasLoadPromise: !!this.loadPromise,
      hasRenderPromise: !!this.renderPromise,

      // MathJax状态
      hasMathJax: !!window.MathJax,
      hasTypesetPromise: !!(window.MathJax && window.MathJax.typesetPromise),
      hasStartup: !!(window.MathJax && window.MathJax.startup),

      // 配置信息
      cdnUrl: this.cdnUrl,
      loadTimeout: this.loadTimeout,
      retryAttempts: this.retryAttempts,
      currentStrategy: this.currentStrategy,

      // 队列状态
      renderQueueSize: this.renderQueue.size,

      // 性能统计
      stats: { ...this.stats },

      // 时间戳
      timestamp: Date.now()
    };
  }

  /**
   * 📈 获取性能统计
   * @returns {Object} 性能统计信息
   */
  getPerformanceStats() {
    const uptime = this.stats.startTime ? Date.now() - this.stats.startTime : 0;

    return {
      ...this.stats,
      uptime,
      averageRenderTime: this.stats.renderCount > 0 ? this.stats.loadTime / this.stats.renderCount : 0,
      errorRate: this.stats.renderCount > 0 ? this.stats.errorCount / this.stats.renderCount : 0,
      successRate: this.stats.renderCount > 0 ? (this.stats.renderCount - this.stats.errorCount) / this.stats.renderCount : 0
    };
  }

  /**
   * 🔧 调试工具：强制重新渲染页面所有数学公式
   * @returns {Promise} 渲染完成Promise
   */
  async debugRenderAll() {
    console.log('🔧 调试工具：强制重新渲染页面所有数学公式');

    try {
      // 查找所有可能包含数学公式的元素
      const candidates = document.querySelectorAll('*');
      const mathElements = [];

      for (const element of candidates) {
        if (this._detectMathElements([element]).length > 0) {
          mathElements.push(element);
        }
      }

      console.log(`🔍 发现 ${mathElements.length} 个包含数学公式的元素`);

      if (mathElements.length > 0) {
        await this.smartRender(mathElements);
      }

      return mathElements.length;
    } catch (error) {
      console.error('🔧 调试渲染失败:', error);
      throw error;
    }
  }

  /**
   * 🔍 调试工具：分析页面数学公式状态
   * @returns {Object} 分析结果
   */
  debugAnalyzePage() {
    console.log('🔍 调试工具：分析页面数学公式状态');

    const analysis = {
      totalElements: 0,
      mathElements: 0,
      renderedFormulas: 0,
      unrenderedFormulas: 0,
      errorFormulas: 0,
      patterns: {},
      samples: []
    };

    // 分析所有元素
    const allElements = document.querySelectorAll('*');
    analysis.totalElements = allElements.length;

    for (const element of allElements) {
      if (!element.innerHTML) continue;

      const content = element.innerHTML;
      let hasMath = false;

      // 检测数学模式
      for (const pattern of this.mathPatterns) {
        const matches = content.match(pattern);
        if (matches) {
          hasMath = true;
          const patternName = pattern.toString();
          analysis.patterns[patternName] = (analysis.patterns[patternName] || 0) + matches.length;
        }
      }

      if (hasMath) {
        analysis.mathElements++;

        // 统计渲染状态
        const renderedCount = element.querySelectorAll('mjx-container').length;
        const errorCount = element.querySelectorAll('mjx-error').length;

        analysis.renderedFormulas += renderedCount;
        analysis.errorFormulas += errorCount;

        if (this._hasUnrenderedMath(content)) {
          analysis.unrenderedFormulas++;
        }

        // 收集样本
        if (analysis.samples.length < 5) {
          analysis.samples.push({
            tag: element.tagName,
            classes: Array.from(element.classList),
            preview: content.substring(0, 100),
            rendered: renderedCount,
            errors: errorCount
          });
        }
      }
    }

    console.log('🔍 页面分析完成:', analysis);
    return analysis;
  }

  /**
   * 🎯 调试工具：测试特定公式渲染
   * @param {string} formula - 要测试的公式
   * @returns {Promise<boolean>} 测试结果
   */
  async debugTestFormula(formula) {
    console.log('🎯 调试工具：测试公式渲染:', formula);

    try {
      // 创建测试元素
      const testElement = document.createElement('div');
      testElement.innerHTML = formula;
      testElement.style.position = 'fixed';
      testElement.style.top = '-1000px';
      testElement.style.left = '-1000px';
      document.body.appendChild(testElement);

      // 尝试渲染
      await this.smartRender([testElement]);

      // 检查结果
      const renderedCount = testElement.querySelectorAll('mjx-container').length;
      const errorCount = testElement.querySelectorAll('mjx-error').length;

      const result = {
        success: renderedCount > 0 && errorCount === 0,
        rendered: renderedCount,
        errors: errorCount,
        html: testElement.innerHTML
      };

      // 清理
      testElement.remove();

      console.log('🎯 公式测试结果:', result);
      return result;

    } catch (error) {
      console.error('🎯 公式测试失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 🚀 创建全局单例
const mathJaxLoader = new MathJaxLoader();

// 🔧 开发环境调试工具
if (process.env.NODE_ENV === 'development') {
  // 将调试工具暴露到全局
  window.mathJaxDebug = {
    loader: mathJaxLoader,
    status: () => mathJaxLoader.getStatus(),
    stats: () => mathJaxLoader.getPerformanceStats(),
    analyze: () => mathJaxLoader.debugAnalyzePage(),
    renderAll: () => mathJaxLoader.debugRenderAll(),
    testFormula: (formula) => mathJaxLoader.debugTestFormula(formula),
    reset: (deep) => mathJaxLoader.reset(deep)
  };

  console.log('🔧 MathJax调试工具已启用，使用 window.mathJaxDebug 访问');
}

export default mathJaxLoader;

/**
 * 🎯 企业级使用示例：
 *
 * import mathJaxLoader from '@/utils/mathJaxLoader'
 *
 * // 基础使用
 * async function renderMath(element) {
 *   try {
 *     // 智能渲染（推荐）
 *     await mathJaxLoader.smartRender(element);
 *   } catch (error) {
 *     console.error('MathJax渲染失败:', error);
 *   }
 * }
 *
 * // 批量渲染
 * async function renderMultiple(elements) {
 *   await mathJaxLoader.smartRender(elements);
 * }
 *
 * // 自定义配置
 * await mathJaxLoader.loadMathJax({
 *   tex: {
 *     packages: {'[+]': ['ams', 'color', 'cancel']},
 *     macros: {
 *       'customCommand': '\\textcolor{blue}{#1}'
 *     }
 *   }
 * });
 *
 * // 状态监控
 * const status = mathJaxLoader.getStatus();
 * const stats = mathJaxLoader.getPerformanceStats();
 *
 * // 调试工具（开发环境）
 * if (process.env.NODE_ENV === 'development') {
 *   // 分析页面数学公式
 *   const analysis = mathJaxLoader.debugAnalyzePage();
 *
 *   // 测试特定公式
 *   const testResult = await mathJaxLoader.debugTestFormula('$$\\color{red}{x = 1}$$');
 *
 *   // 强制重新渲染所有公式
 *   await mathJaxLoader.debugRenderAll();
 * }
 *
 * // 错误处理
 * mathJaxLoader.loadMathJax().catch(error => {
 *   console.error('MathJax加载失败，启用降级模式');
 *   // 应用会自动尝试错误恢复
 * });
 */
