<template>
  <div class="mathjax-test-page">
    <div class="header">
      <h1>🧮 企业级MathJax测试页面</h1>
      <p>测试各种数学公式渲染场景，验证智能修复功能</p>
    </div>

    <div class="controls">
      <el-button @click="runAllTests" type="primary" :loading="testing">
        🚀 运行所有测试
      </el-button>
      <el-button @click="debugRenderAll" type="warning">
        🔧 调试渲染
      </el-button>
      <el-button @click="showStats" type="info">
        📊 查看统计
      </el-button>
      <el-button @click="resetAll" type="danger">
        🧹 重置
      </el-button>
    </div>

    <div class="test-sections">
      <!-- 问题公式测试 -->
      <div class="test-section">
        <h2>🎯 问题公式测试（您遇到的问题）</h2>
        <div class="test-case">
          <h3>原始问题公式：</h3>
          <shuxue :content="problemFormula" />
        </div>
      </div>

      <!-- 颜色命令测试 -->
      <div class="test-section">
        <h2>🎨 颜色命令测试</h2>
        <div class="test-case" v-for="(test, index) in colorTests" :key="index">
          <h3>{{ test.name }}：</h3>
          <shuxue :content="test.content" />
        </div>
      </div>

      <!-- 箭头命令测试 -->
      <div class="test-section">
        <h2>➡️ 箭头命令测试</h2>
        <div class="test-case" v-for="(test, index) in arrowTests" :key="index">
          <h3>{{ test.name }}：</h3>
          <shuxue :content="test.content" />
        </div>
      </div>

      <!-- 复杂公式测试 -->
      <div class="test-section">
        <h2>🔬 复杂公式测试</h2>
        <div class="test-case" v-for="(test, index) in complexTests" :key="index">
          <h3>{{ test.name }}：</h3>
          <shuxue :content="test.content" />
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-panel" v-if="showStatsPanel">
      <h2>📊 渲染统计信息</h2>
      <pre>{{ JSON.stringify(stats, null, 2) }}</pre>
    </div>

    <!-- 测试结果 -->
    <div class="test-results" v-if="testResults.length > 0">
      <h2>🧪 测试结果</h2>
      <div class="result-item" v-for="(result, index) in testResults" :key="index">
        <div class="result-header">
          <span :class="['status', result.success ? 'success' : 'error']">
            {{ result.success ? '✅' : '❌' }}
          </span>
          <span class="test-name">{{ result.name }}</span>
        </div>
        <div class="result-details" v-if="!result.success">
          <p>错误: {{ result.error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Shuxue from '@/components/shuxue.vue';

// 响应式数据
const testing = ref(false);
const showStatsPanel = ref(false);
const stats = ref({});
const testResults = ref([]);

// 您遇到的问题公式
const problemFormula = ref(`
## 🔥 秒杀思路（对称关系三刀流 🗡️）

### 📜 黄金公式

**题干本质：**

$$ \\color{red}{\\text{前组词关系}} \\xrightarrow{\\text{必须镜像}} \\text{后组词关系} $$

**选项筛选：**

1️⃣ **杀关系断裂**：A（前属性后动作）
2️⃣ **杀类型不符**：B（前包含后组成）  
3️⃣ **杀概念错误**：D（史稿≠传记的子类）
4️⃣ **敲对称并列**：C（双组纯并列！）
`);

// 颜色命令测试用例
const colorTests = ref([
  {
    name: '基础颜色命令',
    content: '$$ \\color{red}{红色文字} + \\color{blue}{蓝色文字} $$'
  },
  {
    name: 'textcolor命令',
    content: '$$ \\textcolor{green}{绿色文字} + \\textcolor{purple}{紫色文字} $$'
  },
  {
    name: '混合颜色命令',
    content: '$$ \\color{red}{x} = \\textcolor{blue}{y} + \\color{green}{z} $$'
  }
]);

// 箭头命令测试用例
const arrowTests = ref([
  {
    name: '基础箭头',
    content: '$$ A \\rightarrow B \\leftarrow C $$'
  },
  {
    name: '扩展箭头',
    content: '$$ A \\xrightarrow{\\text{条件}} B $$'
  },
  {
    name: '中文箭头',
    content: '$$ \\text{前组词关系} \\xrightarrow{\\text{必须镜像}} \\text{后组词关系} $$'
  }
]);

// 复杂公式测试用例
const complexTests = ref([
  {
    name: '数学方程',
    content: '$$ \\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi} $$'
  },
  {
    name: '矩阵',
    content: '$$ \\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix} $$'
  },
  {
    name: '分数和根号',
    content: '$$ \\frac{\\sqrt{a^2 + b^2}}{\\sqrt{c^2 + d^2}} $$'
  }
]);

// 运行所有测试
const runAllTests = async () => {
  testing.value = true;
  testResults.value = [];
  
  try {
    console.log('🧪 开始运行MathJax测试套件...');
    
    // 测试公式修复工具
    if (window.mathFormulaFixer) {
      const fixerResult = await testFormulaFixer();
      testResults.value.push(fixerResult);
    }
    
    // 测试MathJax加载器
    if (window.mathJaxDebug) {
      const loaderResult = await testMathJaxLoader();
      testResults.value.push(loaderResult);
    }
    
    // 测试具体公式
    const formulaResults = await testSpecificFormulas();
    testResults.value.push(...formulaResults);
    
    console.log('✅ 测试套件完成');
    
  } catch (error) {
    console.error('❌ 测试套件失败:', error);
    testResults.value.push({
      name: '测试套件',
      success: false,
      error: error.message
    });
  } finally {
    testing.value = false;
  }
};

// 测试公式修复工具
const testFormulaFixer = async () => {
  try {
    const testContent = '$$ \\color{red}{test} \\xrightarrow{中文} result $$';
    const result = window.mathFormulaFixer.smartFix(testContent);
    
    return {
      name: '公式修复工具',
      success: result.hasChanges,
      details: `修复了 ${result.totalFixes} 个问题`
    };
  } catch (error) {
    return {
      name: '公式修复工具',
      success: false,
      error: error.message
    };
  }
};

// 测试MathJax加载器
const testMathJaxLoader = async () => {
  try {
    const status = window.mathJaxDebug.status();
    
    return {
      name: 'MathJax加载器',
      success: status.isLoaded && status.hasMathJax,
      details: `加载状态: ${status.isLoaded ? '已加载' : '未加载'}`
    };
  } catch (error) {
    return {
      name: 'MathJax加载器',
      success: false,
      error: error.message
    };
  }
};

// 测试具体公式
const testSpecificFormulas = async () => {
  const results = [];
  
  // 测试问题公式
  try {
    if (window.mathJaxDebug) {
      const result = await window.mathJaxDebug.testFormula(problemFormula.value);
      results.push({
        name: '问题公式渲染',
        success: result.success,
        details: `渲染: ${result.rendered || 0}, 错误: ${result.errors || 0}`
      });
    }
  } catch (error) {
    results.push({
      name: '问题公式渲染',
      success: false,
      error: error.message
    });
  }
  
  return results;
};

// 调试渲染
const debugRenderAll = async () => {
  try {
    if (window.mathJaxDebug) {
      const count = await window.mathJaxDebug.renderAll();
      console.log(`🔧 强制重新渲染了 ${count} 个元素`);
    }
  } catch (error) {
    console.error('🔧 调试渲染失败:', error);
  }
};

// 显示统计信息
const showStats = () => {
  showStatsPanel.value = !showStatsPanel.value;
  
  if (showStatsPanel.value) {
    stats.value = {
      mathJax: window.mathJaxDebug?.stats() || {},
      formulaFixer: window.mathFormulaFixer?.getStats() || {},
      pageAnalysis: window.mathJaxDebug?.analyze() || {}
    };
  }
};

// 重置所有
const resetAll = async () => {
  try {
    if (window.mathJaxDebug) {
      await window.mathJaxDebug.reset(true);
    }
    if (window.mathFormulaFixer) {
      window.mathFormulaFixer.resetStats();
    }
    
    testResults.value = [];
    showStatsPanel.value = false;
    stats.value = {};
    
    console.log('🧹 重置完成');
  } catch (error) {
    console.error('🧹 重置失败:', error);
  }
};

onMounted(() => {
  console.log('🧮 MathJax测试页面已加载');
});
</script>

<style scoped>
.mathjax-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.test-case {
  margin: 15px 0;
  padding: 15px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.stats-panel {
  margin-top: 30px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.test-results {
  margin-top: 30px;
}

.result-item {
  margin: 10px 0;
  padding: 15px;
  border-radius: 4px;
  background: white;
  border: 1px solid #e0e0e0;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status.success {
  color: #67c23a;
}

.status.error {
  color: #f56c6c;
}

.test-name {
  font-weight: bold;
}

.result-details {
  margin-top: 10px;
  color: #666;
}
</style>
