<template>
  <div class="blockquote-math-wrapper">
    <!-- 原始引用块内容（隐藏数学公式） -->
    <blockquote 
      class="original-blockquote"
      v-html="processedContent"
    ></blockquote>
    
    <!-- 独立渲染的数学公式 -->
    <div 
      v-for="(formula, index) in mathFormulas" 
      :key="index"
      class="math-formula-container"
      :style="{ 
        position: 'absolute',
        top: formula.position.top + 'px',
        left: formula.position.left + 'px',
        zIndex: 10
      }"
    >
      <div 
        :ref="`mathFormula${index}`"
        class="math-formula"
        v-html="formula.latex"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BlockquoteMath',
  props: {
    content: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      mathFormulas: [],
      processedContent: ''
    }
  },
  mounted() {
    this.extractAndRenderMath()
  },
  watch: {
    content() {
      this.extractAndRenderMath()
    }
  },
  methods: {
    extractAndRenderMath() {
      console.log('🔧 BlockquoteMath: 开始提取和渲染数学公式...')
      
      // 1. 提取数学公式
      const mathRegex = /\$\$([^$]+)\$\$/g
      const formulas = []
      let match
      let processedContent = this.content
      
      while ((match = mathRegex.exec(this.content)) !== null) {
        const fullMatch = match[0]
        const latexContent = match[1]
        
        console.log('📐 发现数学公式:', latexContent)
        
        formulas.push({
          original: fullMatch,
          latex: `$$${latexContent}$$`,
          placeholder: `<span class="math-placeholder" data-formula-index="${formulas.length}">🔢 数学公式</span>`,
          position: { top: 0, left: 0 }
        })
      }
      
      // 2. 替换原内容中的数学公式为占位符
      formulas.forEach((formula, index) => {
        processedContent = processedContent.replace(
          formula.original, 
          formula.placeholder
        )
      })
      
      this.mathFormulas = formulas
      this.processedContent = processedContent
      
      console.log(`📊 提取到 ${formulas.length} 个数学公式`)
      
      // 3. 等待DOM更新后渲染数学公式
      this.$nextTick(() => {
        this.renderMathFormulas()
        setTimeout(() => {
          this.positionMathFormulas()
        }, 100)
      })
    },
    
    async renderMathFormulas() {
      console.log('🎨 开始渲染数学公式...')
      
      for (let i = 0; i < this.mathFormulas.length; i++) {
        const formulaRef = this.$refs[`mathFormula${i}`]
        if (formulaRef && formulaRef[0]) {
          const element = formulaRef[0]
          
          try {
            if (window.MathJax && window.MathJax.typesetPromise) {
              await window.MathJax.typesetPromise([element])
              console.log(`✅ 公式 ${i} 渲染成功`)
            } else {
              console.warn('⚠️ MathJax不可用，使用备用渲染')
              element.innerHTML = this.mathFormulas[i].latex
            }
          } catch (error) {
            console.error(`❌ 公式 ${i} 渲染失败:`, error)
            element.innerHTML = this.mathFormulas[i].latex
          }
        }
      }
    },
    
    positionMathFormulas() {
      console.log('📍 开始定位数学公式...')
      
      const placeholders = this.$el.querySelectorAll('.math-placeholder')
      
      placeholders.forEach((placeholder, index) => {
        const rect = placeholder.getBoundingClientRect()
        const containerRect = this.$el.getBoundingClientRect()
        
        if (this.mathFormulas[index]) {
          this.mathFormulas[index].position = {
            top: rect.top - containerRect.top,
            left: rect.left - containerRect.left
          }
        }
      })
      
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.blockquote-math-wrapper {
  position: relative;
  display: block;
}

.original-blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ddd;
  background-color: #f9f9f9;
  font-style: italic;
}

.math-formula-container {
  position: absolute;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.math-formula {
  font-size: 14px;
  line-height: 1.4;
}

.math-placeholder {
  display: inline-block;
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

@media (prefers-color-scheme: dark) {
  .original-blockquote {
    background-color: #2d2d2d;
    border-left-color: #555;
    color: #e0e0e0;
  }
  
  .math-formula-container {
    background: #1e1e1e;
    border-color: #444;
    color: #e0e0e0;
  }
  
  .math-placeholder {
    background: #1565c0;
    color: #bbdefb;
  }
}
</style>
