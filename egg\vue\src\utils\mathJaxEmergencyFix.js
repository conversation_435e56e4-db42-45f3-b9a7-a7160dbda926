/**
 * 🚨 MathJax紧急修复工具
 * 用于解决递归宏定义和渲染错误问题
 */

class MathJaxEmergencyFix {
  constructor() {
    this.isFixing = false;
  }

  /**
   * 🚨 紧急重置MathJax
   */
  async emergencyReset() {
    if (this.isFixing) {
      console.log('🚨 紧急修复正在进行中...');
      return;
    }

    this.isFixing = true;
    console.log('🚨 开始MathJax紧急重置...');

    try {
      // 1. 停止所有MathJax活动
      this.stopAllMathJaxActivity();

      // 2. 清理DOM中的错误元素
      this.cleanupErrorElements();

      // 3. 重置MathJax配置
      this.resetMathJaxConfig();

      // 4. 重新加载MathJax
      await this.reloadMathJax();

      console.log('✅ MathJax紧急重置完成');
    } catch (error) {
      console.error('❌ 紧急重置失败:', error);
    } finally {
      this.isFixing = false;
    }
  }

  /**
   * 停止所有MathJax活动
   */
  stopAllMathJaxActivity() {
    console.log('🛑 停止所有MathJax活动...');

    // 清除所有定时器
    const highestTimeoutId = setTimeout(() => {}, 0);
    for (let i = 0; i < highestTimeoutId; i++) {
      clearTimeout(i);
    }

    // 停止MathJax处理
    if (window.MathJax) {
      try {
        if (window.MathJax.startup) {
          window.MathJax.startup.document.state(0);
        }
      } catch (error) {
        console.warn('停止MathJax处理时出错:', error);
      }
    }
  }

  /**
   * 清理DOM中的错误元素
   */
  cleanupErrorElements() {
    console.log('🧹 清理DOM中的错误元素...');

    // 移除所有MathJax相关元素
    const selectors = [
      'mjx-container',
      'mjx-assistive-mml',
      'mjx-error',
      '.MathJax',
      '.MathJax_Display',
      '.MathJax_Preview'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        try {
          el.remove();
        } catch (error) {
          console.warn(`移除元素 ${selector} 时出错:`, error);
        }
      });
    });

    // 清理包含错误文本的元素
    const textNodes = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const errorTexts = [];
    let node;
    while (node = textNodes.nextNode()) {
      if (node.textContent.includes('\\text{') || 
          node.textContent.includes('\\xrightarrow{') ||
          node.textContent.includes('maximum macro substitution')) {
        errorTexts.push(node);
      }
    }

    errorTexts.forEach(node => {
      try {
        const parent = node.parentElement;
        if (parent && parent.tagName !== 'SCRIPT') {
          // 恢复原始内容或清空
          parent.innerHTML = parent.innerHTML.replace(/\\text\{[^}]*\}/g, '');
          parent.innerHTML = parent.innerHTML.replace(/\\xrightarrow\{[^}]*\}/g, '→');
        }
      } catch (error) {
        console.warn('清理文本节点时出错:', error);
      }
    });
  }

  /**
   * 重置MathJax配置
   */
  resetMathJaxConfig() {
    console.log('🔧 重置MathJax配置...');

    // 删除现有的MathJax对象
    if (window.MathJax) {
      delete window.MathJax;
    }

    // 移除MathJax脚本
    const scripts = document.querySelectorAll('script[src*="mathjax"]');
    scripts.forEach(script => script.remove());

    // 设置安全的MathJax配置
    window.MathJax = {
      tex: {
        inlineMath: [['$', '$'], ['\\(', '\\)']],
        displayMath: [['$$', '$$'], ['\\[', '\\]']],
        processEscapes: true,
        packages: {'[+]': ['ams', 'color']}, // 只加载基础包
        macros: {}, // 清空所有宏定义
        maxMacros: 1000, // 限制宏展开次数
        maxBuffer: 5 * 1024 // 限制缓冲区大小
      },
      svg: {
        fontCache: 'global'
      },
      options: {
        skipHtmlTags: ['script', 'noscript', 'style', 'textarea'],
        ignoreHtmlClass: 'tex2jax_ignore',
        processHtmlClass: 'tex2jax_process'
      },
      startup: {
        ready: () => {
          console.log('🔧 安全的MathJax配置已加载');
          if (window.MathJax.startup.defaultReady) {
            window.MathJax.startup.defaultReady();
          }
        }
      }
    };
  }

  /**
   * 重新加载MathJax
   */
  async reloadMathJax() {
    console.log('📡 重新加载MathJax...');

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js';
      script.async = true;
      script.id = 'MathJax-script';

      script.onload = () => {
        console.log('✅ MathJax重新加载成功');
        resolve();
      };

      script.onerror = (error) => {
        console.error('❌ MathJax重新加载失败:', error);
        reject(error);
      };

      document.head.appendChild(script);
    });
  }

  /**
   * 🔧 修复特定元素
   */
  fixElement(element) {
    if (!element) return;

    try {
      // 清理元素中的错误内容
      let content = element.innerHTML;
      
      // 移除递归的\text{}包装
      content = content.replace(/\\text\{\\text\{([^}]*)\}\}/g, '\\text{$1}');
      
      // 简化复杂的命令
      content = content.replace(/\\xrightarrow\{\\text\{([^}]*)\}\}/g, '\\xrightarrow{$1}');
      
      // 移除过度嵌套
      content = content.replace(/(\{[^{}]*)\{([^{}]*)\}([^{}]*\})/g, '{$2}');
      
      element.innerHTML = content;
      
      console.log('🔧 元素修复完成');
    } catch (error) {
      console.error('🔧 元素修复失败:', error);
    }
  }

  /**
   * 🚨 一键修复页面
   */
  async quickFix() {
    console.log('🚨 开始一键修复...');

    // 1. 紧急重置
    await this.emergencyReset();

    // 2. 等待MathJax加载
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 3. 重新渲染页面
    if (window.MathJax && window.MathJax.typesetPromise) {
      try {
        await window.MathJax.typesetPromise([document.body]);
        console.log('✅ 页面重新渲染完成');
      } catch (error) {
        console.error('❌ 页面重新渲染失败:', error);
      }
    }

    console.log('🎉 一键修复完成！');
  }
}

// 创建全局实例
const mathJaxEmergencyFix = new MathJaxEmergencyFix();

// 暴露到全局
window.mathJaxEmergencyFix = mathJaxEmergencyFix;

export default mathJaxEmergencyFix;

/**
 * 🚨 紧急使用方法：
 * 
 * 在浏览器控制台中执行：
 * 
 * // 一键修复
 * window.mathJaxEmergencyFix.quickFix()
 * 
 * // 或者分步修复
 * window.mathJaxEmergencyFix.emergencyReset()
 */
