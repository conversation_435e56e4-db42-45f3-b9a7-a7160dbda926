/**
 * 🔧 智能数学公式检测和修复工具
 * 功能：自动检测和修复常见的LaTeX公式问题
 * 特性：语法修复、命令转换、错误恢复、智能建议
 * 作者：企业级MathJax解决方案
 */

class MathFormulaFixer {
  constructor() {
    // 常见问题模式和修复规则
    this.fixRules = [
      // 颜色命令修复
      {
        name: 'color-command-fix',
        pattern: /\\color\{([^}]+)\}(?!\{)/g,
        replacement: '\\textcolor{$1}',
        description: '修复 \\color 命令为 \\textcolor'
      },
      
      // 箭头命令修复
      {
        name: 'arrow-text-fix',
        pattern: /\\xrightarrow\{([^{}]*[一-龟][^{}]*)\}/g,
        replacement: '\\xrightarrow{\\text{$1}}',
        description: '为箭头命令中的中文添加 \\text 包装'
      },
      
      // 暂时禁用中文文本修复，避免递归问题
      // {
      //   name: 'chinese-text-fix',
      //   pattern: /([一-龟]+)(?![^$]*\})/g,
      //   replacement: '\\text{$1}',
      //   description: '为裸露的中文添加 \\text 包装'
      // },
      
      // 公式包装修复
      {
        name: 'formula-wrapper-fix',
        pattern: /\$\$\s*([^$]+?)\s*\$\$/g,
        replacement: (match, formula) => {
          const cleanFormula = formula.trim();
          return `$$${cleanFormula}$$`;
        },
        description: '清理公式包装中的多余空格'
      },
      
      // 特殊字符修复
      {
        name: 'special-chars-fix',
        pattern: /[→←↑↓]/g,
        replacement: (match) => {
          const arrowMap = {
            '→': '\\rightarrow',
            '←': '\\leftarrow', 
            '↑': '\\uparrow',
            '↓': '\\downarrow'
          };
          return arrowMap[match] || match;
        },
        description: '转换Unicode箭头为LaTeX命令'
      }
    ];
    
    // 检测模式
    this.detectionPatterns = [
      {
        name: 'unrendered-color',
        pattern: /\$\$[^$]*\\color\{[^}]+\}[^$]*\$\$/,
        severity: 'high',
        description: '检测到可能未渲染的颜色命令'
      },
      {
        name: 'unrendered-arrow',
        pattern: /\$\$[^$]*\\xrightarrow\{[^}]*\}[^$]*\$\$/,
        severity: 'high', 
        description: '检测到可能未渲染的箭头命令'
      },
      {
        name: 'naked-chinese',
        pattern: /\$[^$]*[一-龟]+[^$]*\$/,
        severity: 'medium',
        description: '检测到未包装的中文字符'
      },
      {
        name: 'unicode-arrows',
        pattern: /\$[^$]*[→←↑↓][^$]*\$/,
        severity: 'medium',
        description: '检测到Unicode箭头字符'
      }
    ];
    
    // 统计信息
    this.stats = {
      totalChecks: 0,
      issuesFound: 0,
      issuesFixed: 0,
      fixHistory: []
    };
  }

  /**
   * 🔍 智能检测公式问题
   * @param {string} content - 要检测的内容
   * @returns {Array} 检测到的问题列表
   */
  detectIssues(content) {
    this.stats.totalChecks++;
    const issues = [];
    
    for (const pattern of this.detectionPatterns) {
      const matches = content.match(pattern.pattern);
      if (matches) {
        issues.push({
          type: pattern.name,
          severity: pattern.severity,
          description: pattern.description,
          matches: matches,
          count: matches.length
        });
      }
    }
    
    if (issues.length > 0) {
      this.stats.issuesFound += issues.reduce((sum, issue) => sum + issue.count, 0);
      console.log('🔍 检测到公式问题:', issues);
    }
    
    return issues;
  }

  /**
   * 🔧 自动修复公式问题
   * @param {string} content - 要修复的内容
   * @param {Object} options - 修复选项
   * @returns {Object} 修复结果
   */
  autoFix(content, options = {}) {
    const { enableAll = true, enabledRules = [] } = options;
    let fixedContent = content;
    const appliedFixes = [];
    let totalFixes = 0;
    
    for (const rule of this.fixRules) {
      // 检查是否启用此规则
      if (!enableAll && !enabledRules.includes(rule.name)) {
        continue;
      }
      
      const beforeContent = fixedContent;
      
      if (typeof rule.replacement === 'function') {
        fixedContent = fixedContent.replace(rule.pattern, rule.replacement);
      } else {
        fixedContent = fixedContent.replace(rule.pattern, rule.replacement);
      }
      
      if (beforeContent !== fixedContent) {
        const fixCount = (beforeContent.match(rule.pattern) || []).length;
        appliedFixes.push({
          rule: rule.name,
          description: rule.description,
          count: fixCount
        });
        totalFixes += fixCount;
        console.log(`🔧 应用修复规则: ${rule.name} (${fixCount}处)`);
      }
    }
    
    // 更新统计
    this.stats.issuesFixed += totalFixes;
    this.stats.fixHistory.push({
      timestamp: Date.now(),
      fixes: appliedFixes,
      totalFixes
    });
    
    return {
      originalContent: content,
      fixedContent,
      appliedFixes,
      totalFixes,
      hasChanges: content !== fixedContent
    };
  }

  /**
   * 🎯 智能修复建议
   * @param {Array} issues - 检测到的问题
   * @returns {Array} 修复建议
   */
  getSuggestions(issues) {
    const suggestions = [];
    
    for (const issue of issues) {
      switch (issue.type) {
        case 'unrendered-color':
          suggestions.push({
            type: 'fix',
            priority: 'high',
            message: '建议将 \\color{} 命令替换为 \\textcolor{}',
            action: 'apply-color-fix'
          });
          break;
          
        case 'unrendered-arrow':
          suggestions.push({
            type: 'fix',
            priority: 'high', 
            message: '建议为箭头命令中的中文添加 \\text{} 包装',
            action: 'apply-arrow-fix'
          });
          break;
          
        case 'naked-chinese':
          suggestions.push({
            type: 'warning',
            priority: 'medium',
            message: '建议为中文字符添加 \\text{} 包装以确保正确显示',
            action: 'apply-text-fix'
          });
          break;
          
        case 'unicode-arrows':
          suggestions.push({
            type: 'optimization',
            priority: 'low',
            message: '建议将Unicode箭头替换为LaTeX命令以提高兼容性',
            action: 'apply-arrow-conversion'
          });
          break;
      }
    }
    
    return suggestions;
  }

  /**
   * 📊 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalChecks > 0 ? 
        (this.stats.totalChecks - this.stats.issuesFound) / this.stats.totalChecks : 1,
      fixRate: this.stats.issuesFound > 0 ? 
        this.stats.issuesFixed / this.stats.issuesFound : 0
    };
  }

  /**
   * 🧹 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalChecks: 0,
      issuesFound: 0,
      issuesFixed: 0,
      fixHistory: []
    };
  }

  /**
   * 🔧 添加自定义修复规则
   * @param {Object} rule - 修复规则
   */
  addCustomRule(rule) {
    if (rule.name && rule.pattern && rule.replacement && rule.description) {
      this.fixRules.push(rule);
      console.log(`✅ 添加自定义修复规则: ${rule.name}`);
    } else {
      throw new Error('修复规则必须包含 name, pattern, replacement, description 属性');
    }
  }

  /**
   * 🎯 一键修复（检测 + 修复）
   * @param {string} content - 要处理的内容
   * @param {Object} options - 处理选项
   * @returns {Object} 处理结果
   */
  smartFix(content, options = {}) {
    console.log('🎯 开始智能公式修复...');
    
    // 检测问题
    const issues = this.detectIssues(content);
    
    // 生成建议
    const suggestions = this.getSuggestions(issues);
    
    // 自动修复
    const fixResult = this.autoFix(content, options);
    
    return {
      issues,
      suggestions,
      ...fixResult,
      stats: this.getStats()
    };
  }
}

// 创建全局实例
const mathFormulaFixer = new MathFormulaFixer();

export default mathFormulaFixer;

/**
 * 🎯 使用示例：
 * 
 * import mathFormulaFixer from '@/utils/mathFormulaFixer'
 * 
 * // 智能修复
 * const result = mathFormulaFixer.smartFix(content);
 * if (result.hasChanges) {
 *   console.log('修复了', result.totalFixes, '个问题');
 *   // 使用 result.fixedContent
 * }
 * 
 * // 仅检测问题
 * const issues = mathFormulaFixer.detectIssues(content);
 * 
 * // 获取修复建议
 * const suggestions = mathFormulaFixer.getSuggestions(issues);
 * 
 * // 查看统计
 * const stats = mathFormulaFixer.getStats();
 */
