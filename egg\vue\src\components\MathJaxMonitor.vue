<template>
  <div class="mathjax-monitor" v-if="showMonitor">
    <div class="monitor-header">
      <h3>🔍 MathJax 状态监控</h3>
      <el-button @click="toggleMonitor" size="small" type="text">
        {{ expanded ? '收起' : '展开' }}
      </el-button>
    </div>
    
    <div class="monitor-content" v-show="expanded">
      <!-- 实时状态 -->
      <div class="status-section">
        <h4>📊 实时状态</h4>
        <div class="status-grid">
          <div class="status-item">
            <span class="label">加载状态:</span>
            <span :class="['value', status.isLoaded ? 'success' : 'error']">
              {{ status.isLoaded ? '已加载' : '未加载' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">渲染状态:</span>
            <span :class="['value', status.isRendering ? 'warning' : 'success']">
              {{ status.isRendering ? '渲染中' : '空闲' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">队列大小:</span>
            <span class="value">{{ status.renderQueueSize || 0 }}</span>
          </div>
          <div class="status-item">
            <span class="label">CDN地址:</span>
            <span class="value small">{{ status.cdnUrl || 'N/A' }}</span>
          </div>
        </div>
      </div>

      <!-- 性能统计 -->
      <div class="stats-section">
        <h4>📈 性能统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">加载时间:</span>
            <span class="stat-value">{{ stats.loadTime || 0 }}ms</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">渲染次数:</span>
            <span class="stat-value">{{ stats.renderCount || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">错误次数:</span>
            <span class="stat-value error">{{ stats.errorCount || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">成功率:</span>
            <span class="stat-value">{{ (stats.successRate * 100).toFixed(1) }}%</span>
          </div>
        </div>
      </div>

      <!-- 页面分析 -->
      <div class="analysis-section">
        <h4>🔍 页面分析</h4>
        <div class="analysis-grid">
          <div class="analysis-item">
            <span class="label">数学元素:</span>
            <span class="value">{{ analysis.mathElements || 0 }}</span>
          </div>
          <div class="analysis-item">
            <span class="label">已渲染公式:</span>
            <span class="value success">{{ analysis.renderedFormulas || 0 }}</span>
          </div>
          <div class="analysis-item">
            <span class="label">未渲染公式:</span>
            <span class="value warning">{{ analysis.unrenderedFormulas || 0 }}</span>
          </div>
          <div class="analysis-item">
            <span class="label">错误公式:</span>
            <span class="value error">{{ analysis.errorFormulas || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="actions-section">
        <h4>⚡ 快速操作</h4>
        <div class="actions-grid">
          <el-button @click="forceRender" size="small" type="primary">
            🔄 强制渲染
          </el-button>
          <el-button @click="analyzePage" size="small" type="info">
            🔍 分析页面
          </el-button>
          <el-button @click="resetStats" size="small" type="warning">
            📊 重置统计
          </el-button>
          <el-button @click="exportLogs" size="small" type="success">
            📋 导出日志
          </el-button>
        </div>
      </div>

      <!-- 最近错误 -->
      <div class="errors-section" v-if="recentErrors.length > 0">
        <h4>⚠️ 最近错误</h4>
        <div class="error-list">
          <div 
            class="error-item" 
            v-for="(error, index) in recentErrors.slice(0, 3)" 
            :key="index"
          >
            <span class="error-time">{{ formatTime(error.timestamp) }}</span>
            <span class="error-message">{{ error.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 响应式数据
const showMonitor = ref(false);
const expanded = ref(false);
const status = ref({});
const stats = ref({});
const analysis = ref({});
const recentErrors = ref([]);

// 定时器
let statusTimer = null;
let analysisTimer = null;

// 检查是否在开发环境
const isDevelopment = process.env.NODE_ENV === 'development';

// 切换监控器显示
const toggleMonitor = () => {
  expanded.value = !expanded.value;
};

// 更新状态信息
const updateStatus = () => {
  try {
    if (window.mathJaxDebug) {
      status.value = window.mathJaxDebug.status();
      stats.value = window.mathJaxDebug.stats();
    }
  } catch (error) {
    console.warn('状态更新失败:', error);
  }
};

// 分析页面
const analyzePage = () => {
  try {
    if (window.mathJaxDebug) {
      analysis.value = window.mathJaxDebug.analyze();
      console.log('📊 页面分析完成:', analysis.value);
    }
  } catch (error) {
    console.error('页面分析失败:', error);
    addError('页面分析失败: ' + error.message);
  }
};

// 强制渲染
const forceRender = async () => {
  try {
    if (window.mathJaxDebug) {
      const count = await window.mathJaxDebug.renderAll();
      console.log(`🔄 强制渲染了 ${count} 个元素`);
      
      // 更新分析
      setTimeout(analyzePage, 1000);
    }
  } catch (error) {
    console.error('强制渲染失败:', error);
    addError('强制渲染失败: ' + error.message);
  }
};

// 重置统计
const resetStats = () => {
  try {
    if (window.mathJaxDebug) {
      window.mathJaxDebug.reset(false);
    }
    if (window.mathFormulaFixer) {
      window.mathFormulaFixer.resetStats();
    }
    
    recentErrors.value = [];
    console.log('📊 统计信息已重置');
  } catch (error) {
    console.error('重置统计失败:', error);
  }
};

// 导出日志
const exportLogs = () => {
  try {
    const logData = {
      timestamp: new Date().toISOString(),
      status: status.value,
      stats: stats.value,
      analysis: analysis.value,
      errors: recentErrors.value,
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    const blob = new Blob([JSON.stringify(logData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mathjax-logs-${Date.now()}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    console.log('📋 日志已导出');
  } catch (error) {
    console.error('导出日志失败:', error);
  }
};

// 添加错误记录
const addError = (message) => {
  recentErrors.value.unshift({
    timestamp: Date.now(),
    message
  });
  
  // 只保留最近10个错误
  if (recentErrors.value.length > 10) {
    recentErrors.value = recentErrors.value.slice(0, 10);
  }
};

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString();
};

// 监听全局错误
const handleGlobalError = (event) => {
  if (event.error && event.error.message.includes('MathJax')) {
    addError(event.error.message);
  }
};

onMounted(() => {
  // 只在开发环境显示监控器
  showMonitor.value = isDevelopment;
  
  if (isDevelopment) {
    console.log('🔍 MathJax监控器已启动');
    
    // 初始化数据
    updateStatus();
    analyzePage();
    
    // 定时更新状态
    statusTimer = setInterval(updateStatus, 2000);
    
    // 定时分析页面
    analysisTimer = setInterval(analyzePage, 10000);
    
    // 监听全局错误
    window.addEventListener('error', handleGlobalError);
  }
});

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer);
  }
  if (analysisTimer) {
    clearInterval(analysisTimer);
  }
  
  window.removeEventListener('error', handleGlobalError);
});
</script>

<style scoped>
.mathjax-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-size: 12px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
}

.monitor-header h3 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.monitor-content {
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.status-section,
.stats-section,
.analysis-section,
.actions-section,
.errors-section {
  margin-bottom: 15px;
}

.status-section h4,
.stats-section h4,
.analysis-section h4,
.actions-section h4,
.errors-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #666;
}

.status-grid,
.stats-grid,
.analysis-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5px;
}

.status-item,
.stat-item,
.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.label,
.stat-label {
  color: #666;
  font-weight: 500;
}

.value,
.stat-value {
  font-weight: bold;
}

.value.success,
.stat-value.success {
  color: #67c23a;
}

.value.warning,
.stat-value.warning {
  color: #e6a23c;
}

.value.error,
.stat-value.error {
  color: #f56c6c;
}

.value.small {
  font-size: 10px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.error-list {
  max-height: 100px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  flex-direction: column;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.error-time {
  font-size: 10px;
  color: #999;
}

.error-message {
  color: #f56c6c;
  font-size: 11px;
  margin-top: 2px;
}
</style>
