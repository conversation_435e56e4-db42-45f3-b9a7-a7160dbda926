<script setup>
import { useThemeStore } from '@/store/theme';
import { onMounted, onUnmounted, watch } from 'vue';
import { RouterView, useRoute } from 'vue-router';

const themeStore = useThemeStore();
const route = useRoute();

// 🔍 开发环境检测
const isDevelopment = process.env.NODE_ENV === 'development';

// 🔧 核心功能：监听路由变化，控制主题
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log(`🚦 路由变化: ${oldPath} -> ${newPath}`);

    const isSyPage = newPath.includes('/fb/sy');

    if (isSyPage) {
      // 进入 sy 页面：恢复 sy 页面主题
      console.log('🎨 进入 sy 页面，恢复主题设置');
      themeStore.enterSyPage();
    } else {
      // 离开 sy 页面或进入其他页面：强制白色主题
      console.log('🔒 进入非 sy 页面，强制白色主题');
      themeStore.leaveSyPage();
    }
  },
  { immediate: true }, // 立即执行一次，处理初始路由
);

// 🔧 保留原有的强制白色主题功能（兼容性）
function forcePlainTheme() {
  console.log('🔒 收到强制白色主题事件');
  themeStore.leaveSyPage();
}

onMounted(async () => {
  // 初始化主题系统
  themeStore.initializeTheme();

  // 保留原有的事件监听器（兼容性）
  window.addEventListener('force-plain-theme', forcePlainTheme);

  // 🔍 开发环境下动态导入监控组件
  if (isDevelopment) {
    try {
      const { default: MathJaxMonitor } = await import('@/components/MathJaxMonitor.vue');
      // 组件已动态加载
      console.log('🔍 MathJax监控组件已加载');
    } catch (error) {
      console.warn('⚠️ MathJax监控组件加载失败:', error);
    }
  }

  console.log('🎨 App.vue 已挂载，主题管理系统已启动');
});

onUnmounted(() => {
  window.removeEventListener('force-plain-theme', forcePlainTheme);
});
</script>

<template>
  <div id="app">
    <RouterView />
    <!-- 🔍 MathJax状态监控器将在开发环境下自动显示 -->
  </div>
</template>

<style></style>
